const { pool } = require('../config/database');

// Setup test database
beforeAll(async () => {
  // You might want to use a separate test database
  // For now, we'll use the same database but clean it before tests
  try {
    await pool.query('DELETE FROM comments');
    await pool.query('DELETE FROM posts');
    await pool.query('DELETE FROM users');
    console.log('Test database cleaned');
  } catch (error) {
    console.error('Error cleaning test database:', error);
  }
});

// Cleanup after all tests
afterAll(async () => {
  try {
    await pool.query('DELETE FROM comments');
    await pool.query('DELETE FROM posts');
    await pool.query('DELETE FROM users');
    await pool.end();
    console.log('Test cleanup completed');
  } catch (error) {
    console.error('Error during test cleanup:', error);
  }
});
