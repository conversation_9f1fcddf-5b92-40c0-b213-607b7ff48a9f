{"name": "nodejs-postgresql-api", "version": "1.0.0", "description": "Node.js API with PostgreSQL database", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "db:setup": "node scripts/setup-database.js"}, "keywords": ["nodejs", "postgresql", "api", "express"], "author": "", "license": "MIT", "dependencies": {"express": "^4.18.2", "pg": "^8.11.3", "dotenv": "^16.3.1", "cors": "^2.8.5", "helmet": "^7.1.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "joi": "^17.11.0"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.7.0", "supertest": "^6.3.3"}}