# Node.js PostgreSQL API

Một API RESTful được xây dựng với Node.js, Express và PostgreSQL, bao gồm authentication, CRUD operations và các tính năng bảo mật cơ bản.

## 🚀 Tính năng

- ✅ RESTful API với Express.js
- ✅ PostgreSQL database với connection pooling
- ✅ JWT Authentication & Authorization
- ✅ Password hashing với bcrypt
- ✅ Input validation với Joi
- ✅ Error handling middleware
- ✅ Rate limiting
- ✅ Security headers với Helmet
- ✅ CORS support
- ✅ Logging middleware
- ✅ Environment configuration
- ✅ Database migrations và seeding

## 📋 Yêu cầu hệ thống

- Node.js (v14 hoặc cao hơn)
- PostgreSQL (v12 hoặc cao hơn)
- npm hoặc yarn

## 🛠️ Cài đặt

### 1. Clone repository

```bash
git clone <repository-url>
cd nodejs-postgresql-api
```

### 2. Cài đặt dependencies

```bash
npm install
```

### 3. Cấu hình PostgreSQL

Tạo database PostgreSQL:

```sql
CREATE DATABASE nodejs_postgresql_db;
CREATE USER your_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE nodejs_postgresql_db TO your_user;
```

### 4. Cấu hình environment variables

Sao chép file `.env.example` thành `.env` và cập nhật các giá trị:

```bash
cp .env.example .env
```

Chỉnh sửa file `.env`:

```env
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=nodejs_postgresql_db
DB_USER=your_user
DB_PASSWORD=your_password

# Server Configuration
PORT=3000
NODE_ENV=development

# JWT Configuration
JWT_SECRET=your_very_secure_jwt_secret_key_here
JWT_EXPIRES_IN=24h

# Other Configuration
BCRYPT_ROUNDS=12
```

### 5. Setup database

Chạy script để tạo tables và insert dữ liệu mẫu:

```bash
npm run db:setup
```

### 6. Khởi chạy server

Development mode:
```bash
npm run dev
```

Production mode:
```bash
npm start
```

Server sẽ chạy tại `http://localhost:3000`

## 📚 API Documentation

### Authentication Endpoints

#### Register User
```http
POST /api/auth/register
Content-Type: application/json

{
  "username": "john_doe",
  "email": "<EMAIL>",
  "password": "password123",
  "first_name": "John",
  "last_name": "Doe"
}
```

#### Login
```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

#### Get Profile
```http
GET /api/auth/profile
Authorization: Bearer <jwt_token>
```

#### Change Password
```http
PUT /api/auth/change-password
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "currentPassword": "old_password",
  "newPassword": "new_password"
}
```

### User Management Endpoints

#### Get All Users (Admin only)
```http
GET /api/users?page=1&limit=10
Authorization: Bearer <admin_jwt_token>
```

#### Get User by ID
```http
GET /api/users/:id
Authorization: Bearer <jwt_token>
```

#### Update User
```http
PUT /api/users/:id
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "first_name": "Updated Name",
  "last_name": "Updated Last Name"
}
```

#### Delete User
```http
DELETE /api/users/:id
Authorization: Bearer <jwt_token>
```

### Health Check
```http
GET /health
```

## 🗂️ Cấu trúc dự án

```
├── src/
│   ├── app.js              # Express app configuration
│   └── server.js           # Server entry point
├── config/
│   └── database.js         # Database configuration
├── controllers/
│   ├── authController.js   # Authentication logic
│   └── userController.js   # User management logic
├── middleware/
│   ├── auth.js            # Authentication middleware
│   ├── errorHandler.js    # Error handling middleware
│   ├── logger.js          # Logging middleware
│   ├── rateLimiter.js     # Rate limiting middleware
│   └── validation.js      # Input validation middleware
├── models/
│   └── User.js            # User model
├── routes/
│   ├── authRoutes.js      # Authentication routes
│   └── userRoutes.js      # User management routes
├── scripts/
│   ├── create-database.sql # Database schema
│   └── setup-database.js  # Database setup script
├── tests/
│   └── (test files)
├── .env.example           # Environment variables template
├── .gitignore
├── nodemon.json           # Nodemon configuration
├── package.json
└── README.md
```

## 🧪 Testing

Chạy tests:

```bash
npm test
```

## 🔒 Security Features

- **JWT Authentication**: Secure token-based authentication
- **Password Hashing**: Bcrypt với configurable rounds
- **Rate Limiting**: Ngăn chặn brute force attacks
- **Input Validation**: Joi validation cho tất cả inputs
- **SQL Injection Protection**: Parameterized queries
- **Security Headers**: Helmet.js cho security headers
- **CORS**: Configurable CORS policy

## 📝 Scripts

- `npm start` - Khởi chạy server production
- `npm run dev` - Khởi chạy server development với nodemon
- `npm test` - Chạy test suite
- `npm run db:setup` - Setup database và insert sample data

## 🤝 Contributing

1. Fork the project
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Troubleshooting

### Database Connection Issues

1. Kiểm tra PostgreSQL service đang chạy
2. Verify database credentials trong `.env`
3. Kiểm tra firewall settings
4. Ensure database exists và user có permissions

### JWT Issues

1. Kiểm tra `JWT_SECRET` trong `.env`
2. Verify token format trong Authorization header
3. Check token expiration

### Port Issues

1. Kiểm tra port 3000 có đang được sử dụng không
2. Thay đổi `PORT` trong `.env` nếu cần

## 📞 Support

Nếu bạn gặp vấn đề, hãy tạo issue trong repository hoặc liên hệ qua email.
