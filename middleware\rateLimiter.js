// Simple in-memory rate limiter
// For production, consider using Redis-based rate limiting

const rateLimitStore = new Map();

const createRateLimiter = (options = {}) => {
  const {
    windowMs = 15 * 60 * 1000, // 15 minutes
    max = 100, // limit each IP to 100 requests per windowMs
    message = 'Too many requests from this IP, please try again later.',
    standardHeaders = true, // Return rate limit info in the `RateLimit-*` headers
    legacyHeaders = false, // Disable the `X-RateLimit-*` headers
  } = options;

  return (req, res, next) => {
    const key = req.ip;
    const now = Date.now();
    
    // Clean up old entries
    for (const [ip, data] of rateLimitStore.entries()) {
      if (now - data.resetTime > windowMs) {
        rateLimitStore.delete(ip);
      }
    }
    
    // Get or create rate limit data for this IP
    let rateLimitData = rateLimitStore.get(key);
    
    if (!rateLimitData || now - rateLimitData.resetTime > windowMs) {
      rateLimitData = {
        count: 0,
        resetTime: now
      };
    }
    
    rateLimitData.count++;
    rateLimitStore.set(key, rateLimitData);
    
    // Calculate remaining requests and reset time
    const remaining = Math.max(0, max - rateLimitData.count);
    const resetTime = new Date(rateLimitData.resetTime + windowMs);
    
    // Set headers
    if (standardHeaders) {
      res.set({
        'RateLimit-Limit': max,
        'RateLimit-Remaining': remaining,
        'RateLimit-Reset': resetTime.toISOString()
      });
    }
    
    if (legacyHeaders) {
      res.set({
        'X-RateLimit-Limit': max,
        'X-RateLimit-Remaining': remaining,
        'X-RateLimit-Reset': Math.ceil(resetTime.getTime() / 1000)
      });
    }
    
    // Check if limit exceeded
    if (rateLimitData.count > max) {
      return res.status(429).json({
        status: 'error',
        message: message,
        retryAfter: Math.ceil((resetTime.getTime() - now) / 1000)
      });
    }
    
    next();
  };
};

// Predefined rate limiters
const generalLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100 // limit each IP to 100 requests per windowMs
});

const authLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // limit each IP to 5 auth requests per windowMs
  message: 'Too many authentication attempts, please try again later.'
});

const strictLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10 // limit each IP to 10 requests per windowMs
});

module.exports = {
  createRateLimiter,
  generalLimiter,
  authLimiter,
  strictLimiter
};
