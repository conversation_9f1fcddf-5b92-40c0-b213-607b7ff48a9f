const fs = require('fs');
const path = require('path');
const { pool } = require('../config/database');

const setupDatabase = async () => {
  try {
    console.log('🔄 Setting up database...');
    
    // Read SQL file
    const sqlPath = path.join(__dirname, 'create-database.sql');
    const sql = fs.readFileSync(sqlPath, 'utf8');
    
    // Split SQL commands (simple split by semicolon)
    const commands = sql
      .split(';')
      .map(cmd => cmd.trim())
      .filter(cmd => cmd.length > 0 && !cmd.startsWith('--'));
    
    // Execute each command
    for (const command of commands) {
      if (command.trim()) {
        try {
          await pool.query(command);
          console.log('✅ Executed:', command.substring(0, 50) + '...');
        } catch (err) {
          // Skip errors for already existing objects
          if (err.code === '42P07' || err.code === '42710') {
            console.log('⚠️  Already exists:', command.substring(0, 50) + '...');
          } else {
            throw err;
          }
        }
      }
    }
    
    console.log('✅ Database setup completed successfully!');
    
    // Insert sample data
    await insertSampleData();
    
  } catch (error) {
    console.error('❌ Database setup failed:', error.message);
    throw error;
  } finally {
    await pool.end();
  }
};

const insertSampleData = async () => {
  try {
    console.log('🔄 Inserting sample data...');
    
    // Check if sample data already exists
    const userCheck = await pool.query('SELECT COUNT(*) FROM users');
    if (parseInt(userCheck.rows[0].count) > 0) {
      console.log('⚠️  Sample data already exists, skipping...');
      return;
    }
    
    // Insert sample users
    const bcrypt = require('bcryptjs');
    const hashedPassword = await bcrypt.hash('password123', 12);
    
    await pool.query(`
      INSERT INTO users (username, email, password_hash, first_name, last_name, role)
      VALUES 
        ('admin', '<EMAIL>', $1, 'Admin', 'User', 'admin'),
        ('john_doe', '<EMAIL>', $1, 'John', 'Doe', 'user'),
        ('jane_smith', '<EMAIL>', $1, 'Jane', 'Smith', 'user')
    `, [hashedPassword]);
    
    console.log('✅ Sample users created');
    
    // Get user IDs for posts
    const users = await pool.query('SELECT id, username FROM users');
    const adminUser = users.rows.find(u => u.username === 'admin');
    const johnUser = users.rows.find(u => u.username === 'john_doe');
    
    // Insert sample posts
    await pool.query(`
      INSERT INTO posts (title, content, author_id, status, published_at)
      VALUES 
        ('Welcome to our API', 'This is the first post in our system.', $1, 'published', CURRENT_TIMESTAMP),
        ('Getting Started Guide', 'Here is how to get started with our API.', $1, 'published', CURRENT_TIMESTAMP),
        ('Draft Post', 'This is a draft post.', $2, 'draft', NULL)
    `, [adminUser.id, johnUser.id]);
    
    console.log('✅ Sample posts created');
    console.log('✅ Sample data insertion completed!');
    
  } catch (error) {
    console.error('❌ Sample data insertion failed:', error.message);
    throw error;
  }
};

// Run setup if called directly
if (require.main === module) {
  setupDatabase()
    .then(() => {
      console.log('🎉 Database setup completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Setup failed:', error.message);
      process.exit(1);
    });
}

module.exports = { setupDatabase };
