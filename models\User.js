const { query, transaction } = require('../config/database');
const bcrypt = require('bcryptjs');

class User {
  constructor(data) {
    this.id = data.id;
    this.username = data.username;
    this.email = data.email;
    this.password_hash = data.password_hash;
    this.first_name = data.first_name;
    this.last_name = data.last_name;
    this.role = data.role;
    this.is_active = data.is_active;
    this.created_at = data.created_at;
    this.updated_at = data.updated_at;
  }

  // Create a new user
  static async create(userData) {
    const { username, email, password, first_name, last_name, role = 'user' } = userData;
    
    // Hash password
    const password_hash = await bcrypt.hash(password, parseInt(process.env.BCRYPT_ROUNDS) || 12);
    
    const result = await query(`
      INSERT INTO users (username, email, password_hash, first_name, last_name, role)
      VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING id, username, email, first_name, last_name, role, is_active, created_at, updated_at
    `, [username, email, password_hash, first_name, last_name, role]);
    
    return new User(result.rows[0]);
  }

  // Find user by ID
  static async findById(id) {
    const result = await query(
      'SELECT * FROM users WHERE id = $1 AND is_active = true',
      [id]
    );
    
    return result.rows.length > 0 ? new User(result.rows[0]) : null;
  }

  // Find user by email
  static async findByEmail(email) {
    const result = await query(
      'SELECT * FROM users WHERE email = $1 AND is_active = true',
      [email]
    );
    
    return result.rows.length > 0 ? new User(result.rows[0]) : null;
  }

  // Find user by username
  static async findByUsername(username) {
    const result = await query(
      'SELECT * FROM users WHERE username = $1 AND is_active = true',
      [username]
    );
    
    return result.rows.length > 0 ? new User(result.rows[0]) : null;
  }

  // Get all users with pagination
  static async findAll(page = 1, limit = 10) {
    const offset = (page - 1) * limit;
    
    const result = await query(`
      SELECT id, username, email, first_name, last_name, role, is_active, created_at, updated_at
      FROM users 
      WHERE is_active = true
      ORDER BY created_at DESC
      LIMIT $1 OFFSET $2
    `, [limit, offset]);
    
    const countResult = await query('SELECT COUNT(*) FROM users WHERE is_active = true');
    const total = parseInt(countResult.rows[0].count);
    
    return {
      users: result.rows.map(row => new User(row)),
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    };
  }

  // Update user
  async update(updateData) {
    const allowedFields = ['username', 'email', 'first_name', 'last_name', 'role'];
    const updates = [];
    const values = [];
    let paramCount = 1;

    for (const [key, value] of Object.entries(updateData)) {
      if (allowedFields.includes(key) && value !== undefined) {
        updates.push(`${key} = $${paramCount}`);
        values.push(value);
        paramCount++;
      }
    }

    if (updates.length === 0) {
      throw new Error('No valid fields to update');
    }

    values.push(this.id);
    
    const result = await query(`
      UPDATE users 
      SET ${updates.join(', ')}
      WHERE id = $${paramCount} AND is_active = true
      RETURNING id, username, email, first_name, last_name, role, is_active, created_at, updated_at
    `, values);

    if (result.rows.length === 0) {
      throw new Error('User not found or inactive');
    }

    // Update current instance
    Object.assign(this, result.rows[0]);
    return this;
  }

  // Change password
  async changePassword(newPassword) {
    const password_hash = await bcrypt.hash(newPassword, parseInt(process.env.BCRYPT_ROUNDS) || 12);
    
    await query(
      'UPDATE users SET password_hash = $1 WHERE id = $2',
      [password_hash, this.id]
    );
    
    this.password_hash = password_hash;
    return this;
  }

  // Verify password
  async verifyPassword(password) {
    return await bcrypt.compare(password, this.password_hash);
  }

  // Soft delete user
  async delete() {
    await query(
      'UPDATE users SET is_active = false WHERE id = $1',
      [this.id]
    );
    
    this.is_active = false;
    return this;
  }

  // Get user's public data (without sensitive info)
  toJSON() {
    const { password_hash, ...publicData } = this;
    return publicData;
  }
}

module.exports = User;
