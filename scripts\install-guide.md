# Hướng dẫn cài đặt và chạy dự án

## 1. Cài đặt Node.js

### Windows:
1. <PERSON><PERSON><PERSON> cập https://nodejs.org/
2. Tải phiên bản LTS (Long Term Support)
3. Chạy file installer và làm theo hướng dẫn
4. Mở Command Prompt hoặc PowerShell và chạy:
   ```
   node --version
   npm --version
   ```

### macOS:
```bash
# Sử dụng Homebrew
brew install node

# Hoặc tải từ nodejs.org
```

### Linux (Ubuntu/Debian):
```bash
curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -
sudo apt-get install -y nodejs
```

## 2. Cài đặt PostgreSQL

### Windows:
1. Truy cập https://www.postgresql.org/download/windows/
2. Tải PostgreSQL installer
3. Chạy installer và làm theo hướng dẫn
4. Ghi nhớ password cho user postgres

### macOS:
```bash
# Sử dụng Homebrew
brew install postgresql
brew services start postgresql
```

### Linux (Ubuntu/Debian):
```bash
sudo apt update
sudo apt install postgresql postgresql-contrib
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

## 3. Thiết lập PostgreSQL

### Tạo database và user:
```sql
-- Kết nối với PostgreSQL
psql -U postgres

-- Tạo database
CREATE DATABASE nodejs_postgresql_db;

-- Tạo user (tùy chọn)
CREATE USER your_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE nodejs_postgresql_db TO your_user;

-- Thoát
\q
```

## 4. Cài đặt dự án

### Bước 1: Cài đặt dependencies
```bash
npm install
```

### Bước 2: Cấu hình environment
```bash
# Sao chép file cấu hình mẫu
cp .env.example .env

# Chỉnh sửa file .env với thông tin database của bạn
```

### Bước 3: Setup database
```bash
npm run db:setup
```

### Bước 4: Chạy dự án
```bash
# Development mode
npm run dev

# Production mode
npm start
```

## 5. Kiểm tra cài đặt

### Kiểm tra server:
Truy cập http://localhost:3000/health

### Kiểm tra API:
```bash
# Test register
curl -X POST http://localhost:3000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "password123",
    "first_name": "Test",
    "last_name": "User"
  }'

# Test login
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

## 6. Troubleshooting

### Lỗi "npm command not found":
- Cài đặt lại Node.js
- Kiểm tra PATH environment variable

### Lỗi kết nối PostgreSQL:
- Kiểm tra PostgreSQL service đang chạy
- Verify thông tin database trong .env
- Kiểm tra firewall settings

### Lỗi "execution policy" trên Windows:
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### Lỗi port đã được sử dụng:
- Thay đổi PORT trong file .env
- Hoặc kill process đang sử dụng port 3000

## 7. Công cụ hỗ trợ

### Postman:
- Tải Postman để test API
- Import collection từ file docs/postman-collection.json (nếu có)

### Database GUI:
- pgAdmin: https://www.pgadmin.org/
- DBeaver: https://dbeaver.io/
- TablePlus: https://tableplus.com/

### Code Editor:
- Visual Studio Code: https://code.visualstudio.com/
- Extensions khuyến nghị:
  - REST Client
  - PostgreSQL
  - Node.js Extension Pack
