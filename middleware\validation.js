const Joi = require('joi');

// Generic validation middleware
const validate = (schema, property = 'body') => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req[property]);
    
    if (error) {
      return res.status(400).json({
        status: 'error',
        message: 'Validation error',
        details: error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message
        }))
      });
    }
    
    // Replace the original data with validated data
    req[property] = value;
    next();
  };
};

// Common validation schemas
const schemas = {
  // UUID validation
  uuid: Joi.string().uuid().required(),
  
  // Pagination validation
  pagination: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(10)
  }),
  
  // User validation schemas
  user: {
    register: Joi.object({
      username: Joi.string().alphanum().min(3).max(30).required(),
      email: Joi.string().email().required(),
      password: Joi.string().min(6).required(),
      first_name: Joi.string().max(50).optional(),
      last_name: Joi.string().max(50).optional()
    }),
    
    login: Joi.object({
      email: Joi.string().email().required(),
      password: Joi.string().required()
    }),
    
    update: Joi.object({
      username: Joi.string().alphanum().min(3).max(30).optional(),
      email: Joi.string().email().optional(),
      first_name: Joi.string().max(50).optional(),
      last_name: Joi.string().max(50).optional(),
      role: Joi.string().valid('user', 'admin').optional()
    }),
    
    changePassword: Joi.object({
      currentPassword: Joi.string().required(),
      newPassword: Joi.string().min(6).required()
    })
  }
};

// Specific validation middleware functions
const validateUUID = (paramName = 'id') => {
  return (req, res, next) => {
    const { error } = schemas.uuid.validate(req.params[paramName]);
    
    if (error) {
      return res.status(400).json({
        status: 'error',
        message: `Invalid ${paramName} format`
      });
    }
    
    next();
  };
};

const validatePagination = validate(schemas.pagination, 'query');

// User-specific validations
const validateUserRegistration = validate(schemas.user.register);
const validateUserLogin = validate(schemas.user.login);
const validateUserUpdate = validate(schemas.user.update);
const validatePasswordChange = validate(schemas.user.changePassword);

module.exports = {
  validate,
  validateUUID,
  validatePagination,
  validateUserRegistration,
  validateUserLogin,
  validateUserUpdate,
  validatePasswordChange,
  schemas
};
